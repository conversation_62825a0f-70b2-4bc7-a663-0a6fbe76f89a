<template>
  <div class="pt-16">
    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-dark-300 via-dark-200 to-dark-400 relative overflow-hidden">
      <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue/10 rounded-full blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl"></div>
      </div>

      <div class="max-w-7xl mx-auto relative z-10">
        <div class="text-center mb-8">
          <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
            <span class="text-gradient">{{ guide?.title || '加载中...' }}</span>
          </h1>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {{ guide?.description || '' }}
          </p>
          <div class="text-gray-400">
            最后更新：{{ guide?.lastUpdated || '' }}
          </div>
        </div>

        <!-- Back Button -->
        <div class="text-center">
          <RouterLink to="/support" class="btn-secondary inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            返回技术支持
          </RouterLink>
        </div>
      </div>
    </section>

    <!-- Content Section -->
    <section class="section-padding bg-dark-200">
      <div class="max-w-4xl mx-auto">
        <div class="card-glass p-8">
          <div v-if="guide" class="prose prose-invert max-w-none">
            <div class="text-gray-300 leading-relaxed" v-html="formattedContent"></div>
          </div>

          <div v-else class="text-center text-gray-400">
            <p>指南不存在或正在加载中...</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { getGuideById } from '../data/guides.ts'

const route = useRoute()

const guide = computed(() => {
  return getGuideById(route.params.id as string)
})

// 将Markdown格式的内容转换为HTML
const formattedContent = computed(() => {
  if (!guide.value?.content) return ''

  const content = guide.value.content
  const lines = content.split('\n')
  const result: string[] = []
  let inList = false
  let listType = ''

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    // 处理标题
    if (line.startsWith('### ')) {
      if (inList) {
        result.push(`</${listType}>`)
        inList = false
      }
      result.push(`<h3 class="text-xl font-bold text-white mt-8 mb-4">${line.substring(4)}</h3>`)
    } else if (line.startsWith('## ')) {
      if (inList) {
        result.push(`</${listType}>`)
        inList = false
      }
      result.push(`<h2 class="text-2xl font-bold text-white mt-10 mb-6">${line.substring(3)}</h2>`)
    } else if (line.startsWith('# ')) {
      if (inList) {
        result.push(`</${listType}>`)
        inList = false
      }
      result.push(`<h1 class="text-3xl font-bold text-white mt-12 mb-8">${line.substring(2)}</h1>`)
    }
    // 处理无序列表
    else if (line.startsWith('- ')) {
      if (!inList || listType !== 'ul') {
        if (inList) result.push(`</${listType}>`)
        result.push('<ul class="mb-4 ml-4 space-y-2">')
        inList = true
        listType = 'ul'
      }
      const content = line.substring(2).replace(/\*\*(.*?)\*\*/g, '<strong class="text-white font-semibold">$1</strong>')
      result.push(`<li class="text-gray-300 list-disc list-inside">${content}</li>`)
    }
    // 处理有序列表
    else if (/^\d+\. /.test(line)) {
      if (!inList || listType !== 'ol') {
        if (inList) result.push(`</${listType}>`)
        result.push('<ol class="mb-4 ml-4 space-y-2">')
        inList = true
        listType = 'ol'
      }
      const content = line.replace(/^\d+\. /, '').replace(/\*\*(.*?)\*\*/g, '<strong class="text-white font-semibold">$1</strong>')
      const number = line.match(/^(\d+)\./)?.[1] || '1'
      result.push(`<li class="text-gray-300 list-decimal list-inside">${content}</li>`)
    }
    // 处理空行
    else if (line.trim() === '') {
      if (inList) {
        result.push(`</${listType}>`)
        inList = false
      }
      // 只在非空内容后添加空行
      if (result.length > 0 && !result[result.length - 1].includes('</')) {
        result.push('<br>')
      }
    }
    // 处理普通段落
    else if (line.trim() !== '') {
      if (inList) {
        result.push(`</${listType}>`)
        inList = false
      }
      const content = line.replace(/\*\*(.*?)\*\*/g, '<strong class="text-white font-semibold">$1</strong>')
      result.push(`<p class="mb-4 text-gray-300">${content}</p>`)
    }
  }

  // 关闭未关闭的列表
  if (inList) {
    result.push(`</${listType}>`)
  }

  return result.join('\n')
})
</script>

<style scoped>
.prose {
  color: #e5e7eb;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #ffffff;
}

.prose strong {
  color: #ffffff;
}

.prose a {
  color: #00d4ff;
}

.prose a:hover {
  color: #ffffff;
}
</style>
