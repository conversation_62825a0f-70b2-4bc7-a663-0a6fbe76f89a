# DMATop 官方网站

DMATop专业DMA硬件解决方案官方网站，基于Vue 3 + Vite + Tailwind CSS构建。

## 项目特色

- 🚀 **现代技术栈**: Vue 3 Composition API + TypeScript + Vite
- 🎨 **精美设计**: Tailwind CSS + 深色主题 + 高级动画效果
- 📱 **响应式设计**: 完美适配桌面端和移动端
- ⚡ **高性能**: Vite构建工具，快速开发和部署
- 🌐 **中文支持**: 完整的中文界面和内容

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 6
- **样式框架**: Tailwind CSS
- **动画库**: GSAP
- **图标库**: Heroicons
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **类型检查**: TypeScript
- **代码规范**: ESLint

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## 部署

### Vercel 部署

本项目已配置好Vercel部署，只需：

1. 将代码推送到GitHub仓库
2. 在Vercel中导入GitHub仓库
3. Vercel会自动检测配置并部署

### 手动部署

```sh
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

构建文件将生成在 `dist/` 目录中。

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── HeroSection.vue     # 首页英雄区域
│   ├── ProductOverview.vue # 产品概览
│   ├── KeyAdvantages.vue   # 核心优势
│   ├── Testimonials.vue    # 客户评价
│   └── ...
├── views/              # 页面组件
│   ├── HomeView.vue        # 首页
│   ├── ProductsView.vue    # 产品页面
│   ├── SupportView.vue     # 技术支持
│   └── ContactView.vue     # 联系我们
├── data/               # 数据文件
├── router/             # 路由配置
└── assets/             # 静态资源
```

## 开发说明

- 所有内容数据存储在JS文件中，便于维护和更新
- 使用Composition API编写组件，代码更清晰
- 遵循响应式设计原则，确保各设备兼容性
- 使用GSAP实现流畅的动画效果

## 许可证

© 2025 DMATop. All rights reserved.
