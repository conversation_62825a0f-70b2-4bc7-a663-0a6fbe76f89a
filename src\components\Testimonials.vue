<template>
  <section class="section-padding bg-dark-200">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
          <span class="text-gradient">客户</span>怎么说
        </h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">
          来自真实客户的使用体验和专业评价。
        </p>
      </div>

      <!-- Testimonials Carousel -->
      <div class="relative">
        <div class="overflow-hidden">
          <div 
            class="flex transition-transform duration-500 ease-in-out"
            :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
          >
            <div 
              v-for="(testimonial, index) in testimonials" 
              :key="index"
              class="w-full flex-shrink-0"
            >
              <div class="max-w-4xl mx-auto">
                <div class="card-glass text-center p-8 md:p-12">
                  <!-- Quote -->
                  <div class="mb-8">
                    <svg class="w-12 h-12 text-neon-blue mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                    </svg>
                    <blockquote class="text-2xl md:text-3xl text-white font-light leading-relaxed mb-8">
                      "{{ testimonial.quote }}"
                    </blockquote>
                  </div>

                  <!-- Author -->
                  <div class="text-center">
                    <div class="text-white font-semibold text-lg">{{ testimonial.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Arrows -->
        <button 
          @click="prevSlide"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-all duration-200"
        >
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <button 
          @click="nextSlide"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-all duration-200"
        >
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>

        <!-- Dots Indicator -->
        <div class="flex justify-center mt-8 space-x-2">
          <button
            v-for="(_, index) in testimonials"
            :key="index"
            @click="currentSlide = index"
            class="w-3 h-3 rounded-full transition-all duration-200"
            :class="currentSlide === index ? 'bg-neon-blue' : 'bg-gray-600 hover:bg-gray-500'"
          ></button>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div class="stat-item">
          <div class="text-4xl font-bold text-neon-blue mb-2">100+</div>
          <div class="text-gray-400">满意客户</div>
        </div>
        <div class="stat-item">
          <div class="text-4xl font-bold text-neon-purple mb-2">99.5%</div>
          <div class="text-gray-400">正常运行时间</div>
        </div>
        <div class="stat-item">
          <div class="text-4xl font-bold text-neon-green mb-2">24/7</div>
          <div class="text-gray-400">技术支持</div>
        </div>
        <div class="stat-item">
          <div class="text-4xl font-bold text-neon-pink mb-2">10+</div>
          <div class="text-gray-400">服务地区</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentSlide = ref(0)
let autoSlideInterval: number

const testimonials = [
  {
    quote: "用了DMA的产品半年多了，真的很稳定，从来没出过问题。客服态度也很好，有问题都能及时解决。",
    name: "匿名用户A"
  },
  {
    quote: "朋友推荐的DMA，用下来感觉挺靠谱的。技术支持很专业，遇到问题都能帮忙解决。值得信赖。",
    name: "匿名用户C"
  }
]

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % testimonials.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? testimonials.length - 1 : currentSlide.value - 1
}

onMounted(() => {
  // Auto-slide every 5 seconds
  autoSlideInterval = setInterval(nextSlide, 5000)
})

onUnmounted(() => {
  if (autoSlideInterval) {
    clearInterval(autoSlideInterval)
  }
})
</script>

<style scoped>
.stat-item {
  @apply opacity-0 transform translate-y-4;
  animation: fadeInUp 0.6s ease-out forwards;
}

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
