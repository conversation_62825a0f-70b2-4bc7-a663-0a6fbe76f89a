<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f172a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f472b6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#bgGradient)" stroke="url(#chipGradient)" stroke-width="2" opacity="0.8"/>

  <!-- Main Processor/Chip -->
  <rect x="50" y="50" width="100" height="100" rx="12" fill="url(#chipGradient)" filter="url(#glow)"/>

  <!-- Chip Surface -->
  <rect x="60" y="60" width="80" height="80" rx="8" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>

  <!-- Circuit Traces -->
  <g stroke="rgba(255,255,255,0.8)" stroke-width="2" fill="none">
    <!-- Horizontal traces -->
    <path d="M20 70 L50 70"/>
    <path d="M20 100 L50 100"/>
    <path d="M20 130 L50 130"/>
    <path d="M150 70 L180 70"/>
    <path d="M150 100 L180 100"/>
    <path d="M150 130 L180 130"/>

    <!-- Vertical traces -->
    <path d="M70 20 L70 50"/>
    <path d="M100 20 L100 50"/>
    <path d="M130 20 L130 50"/>
    <path d="M70 150 L70 180"/>
    <path d="M100 150 L100 180"/>
    <path d="M130 150 L130 180"/>
  </g>

  <!-- Connection Pads -->
  <g fill="rgba(255,255,255,0.9)">
    <!-- Left side -->
    <rect x="15" y="65" width="10" height="10" rx="2"/>
    <rect x="15" y="95" width="10" height="10" rx="2"/>
    <rect x="15" y="125" width="10" height="10" rx="2"/>

    <!-- Right side -->
    <rect x="175" y="65" width="10" height="10" rx="2"/>
    <rect x="175" y="95" width="10" height="10" rx="2"/>
    <rect x="175" y="125" width="10" height="10" rx="2"/>

    <!-- Top side -->
    <rect x="65" y="15" width="10" height="10" rx="2"/>
    <rect x="95" y="15" width="10" height="10" rx="2"/>
    <rect x="125" y="15" width="10" height="10" rx="2"/>

    <!-- Bottom side -->
    <rect x="65" y="175" width="10" height="10" rx="2"/>
    <rect x="95" y="175" width="10" height="10" rx="2"/>
    <rect x="125" y="175" width="10" height="10" rx="2"/>
  </g>

  <!-- Central Processing Grid -->
  <g fill="rgba(255,255,255,0.7)">
    <rect x="80" y="80" width="6" height="6" rx="1"/>
    <rect x="90" y="80" width="6" height="6" rx="1"/>
    <rect x="100" y="80" width="6" height="6" rx="1"/>
    <rect x="110" y="80" width="6" height="6" rx="1"/>

    <rect x="80" y="90" width="6" height="6" rx="1"/>
    <rect x="90" y="90" width="6" height="6" rx="1"/>
    <rect x="100" y="90" width="6" height="6" rx="1"/>
    <rect x="110" y="90" width="6" height="6" rx="1"/>

    <rect x="80" y="100" width="6" height="6" rx="1"/>
    <rect x="90" y="100" width="6" height="6" rx="1"/>
    <rect x="100" y="100" width="6" height="6" rx="1"/>
    <rect x="110" y="100" width="6" height="6" rx="1"/>

    <rect x="80" y="110" width="6" height="6" rx="1"/>
    <rect x="90" y="110" width="6" height="6" rx="1"/>
    <rect x="100" y="110" width="6" height="6" rx="1"/>
    <rect x="110" y="110" width="6" height="6" rx="1"/>
  </g>

  <!-- Status Indicators -->
  <circle cx="160" cy="40" r="8" fill="url(#accentGradient)" opacity="0.9"/>
  <circle cx="160" cy="40" r="4" fill="rgba(255,255,255,0.8)"/>

  <!-- DMA Label -->
  <text x="100" y="170" text-anchor="middle" fill="rgba(255,255,255,0.6)" font-family="monospace" font-size="12" font-weight="bold">DMA</text>
</svg>
