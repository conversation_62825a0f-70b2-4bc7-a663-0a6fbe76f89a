<template>
  <div class="pt-16">
    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-dark-300 via-dark-200 to-dark-400 relative overflow-hidden">
      <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue/10 rounded-full blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl"></div>
      </div>

      <div class="max-w-7xl mx-auto relative z-10 text-center">
        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
          联系 <span class="text-gradient">DMATop</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          通过腾讯QQ与DMATop专业团队取得联系，获得一对一的DMA解决方案咨询和技术支持服务。
          <span class="block mt-2 text-neon-blue font-semibold">一年运营经验，专业客服团队为您服务</span>
        </p>
      </div>
    </section>

    <!-- QQ Contact Section -->
    <section class="section-padding bg-dark-200">
      <div class="max-w-4xl mx-auto">
        <div class="card-glass p-12 text-center">
          <div class="mb-8">
            <!-- QQ Logo -->
            <div class="w-24 h-24 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
              </svg>
            </div>
            <h2 class="text-4xl font-bold text-white mb-4">DMATop 专业客服</h2>
            <p class="text-xl text-gray-300 mb-8">
              添加DMATop官方QQ，享受专业的DMA硬件解决方案咨询和一对一技术支持服务
            </p>
          </div>

          <!-- QQ Contact Info -->
          <div class="space-y-8">
            <!-- Main QQ -->
            <div class="bg-white/5 rounded-2xl p-8 border border-white/10">
              <h3 class="text-2xl font-bold text-white mb-4">主客服QQ</h3>
              <div class="flex items-center justify-center space-x-4 mb-6">
                <div class="text-4xl font-bold text-neon-blue">859493291</div>
                <button
                  @click="copyQQ('859493291')"
                  class="btn-primary px-6 py-2 text-sm"
                >
                  复制QQ号
                </button>
              </div>
              <p class="text-gray-300 mb-4">工作时间：周一至周五 9:00-18:00</p>
              <a
                href="tencent://message/?uin=859493291&Site=&Menu=yes"
                class="btn-primary inline-flex items-center px-8 py-3 text-lg"
              >
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                </svg>
                立即联系
              </a>
            </div>

            <!-- Secondary QQ -->
            <div class="bg-white/5 rounded-2xl p-8 border border-white/10">
              <h3 class="text-2xl font-bold text-white mb-4">副客服QQ</h3>
              <div class="flex items-center justify-center space-x-4 mb-6">
                <div class="text-4xl font-bold text-neon-purple">969166882</div>
                <button
                  @click="copyQQ('969166882')"
                  class="btn-secondary px-6 py-2 text-sm"
                >
                  复制QQ号
                </button>
              </div>
              <p class="text-gray-300 mb-4">工作时间：周一至周日 24小时在线</p>
              <a
                href="tencent://message/?uin=969166882&Site=&Menu=yes"
                class="btn-secondary inline-flex items-center px-8 py-3 text-lg"
              >
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                </svg>
                立即联系
              </a>
            </div>
          </div>

          <!-- Instructions -->
          <div class="mt-12 bg-gradient-to-r from-neon-blue/10 to-neon-purple/10 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-white mb-4">联系说明</h3>
            <div class="text-left space-y-3 text-gray-300">
              <p>• 点击"立即联系"按钮将自动打开QQ聊天窗口</p>
              <p>• 如果无法自动打开，请复制QQ号手动添加好友</p>
              <p>• 客服QQ处理产品咨询、技术支持、销售等所有相关问题</p>
              <p>• 请在联系时说明您的具体需求，以便我们更好地为您服务</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
const copyQQ = (qqNumber: string) => {
  navigator.clipboard.writeText(qqNumber).then(() => {
    alert(`QQ号 ${qqNumber} 已复制到剪贴板`)
  }).catch(() => {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = qqNumber
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    alert(`QQ号 ${qqNumber} 已复制到剪贴板`)
  })
}
</script>

<style scoped>
/* QQ Contact specific styles */
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

/* Animation for QQ numbers */
.text-4xl {
  transition: all 0.3s ease;
}

.text-4xl:hover {
  transform: scale(1.05);
}
</style>
