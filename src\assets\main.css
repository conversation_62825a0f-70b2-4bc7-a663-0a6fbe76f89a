@import './base.css';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-300 text-white font-sans antialiased;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.02"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-neon-blue to-neon-purple text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-neon-blue/25 hover:scale-105;
  }

  .btn-secondary {
    @apply border-2 border-neon-blue text-neon-blue font-semibold py-3 px-8 rounded-lg transition-all duration-300 hover:bg-neon-blue hover:text-dark-300 hover:shadow-lg hover:shadow-neon-blue/25;
  }

  .card-glass {
    @apply bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 transition-all duration-300 hover:bg-white/10 hover:border-white/20;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent;
  }

  .glow-effect {
    @apply shadow-lg shadow-neon-blue/20;
  }

  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}
