<template>
  <div class="pt-16">
    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-dark-300 via-dark-200 to-dark-400 relative overflow-hidden">
      <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue/10 rounded-full blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl"></div>
      </div>
      
      <div class="max-w-7xl mx-auto relative z-10 text-center">
        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
          <span class="text-gradient">DMATop</span> 专业套餐
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          DMATop提供业界领先的完整DMA硬件解决方案套餐，包含75T单板、Kmbox网络、6代融合器和专业定制固件服务。
          <span class="block mt-2 text-neon-blue font-semibold">一年运营经验，专业品质保障</span>
        </p>
      </div>
    </section>

    <!-- Product Categories -->
    <section class="section-padding bg-dark-200">
      <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          <div 
            v-for="(product, index) in products" 
            :key="product.id"
            :id="product.id"
            class="product-detail-card"
          >
            <div class="card-glass h-full">
              <!-- Product Header -->
              <div class="mb-6">
                <div class="w-20 h-20 bg-gradient-to-r from-neon-blue to-neon-purple rounded-2xl flex items-center justify-center mb-4">
                  <component :is="product.icon" class="w-10 h-10 text-white" />
                </div>
                <h2 class="text-3xl font-bold text-white mb-2">{{ product.name }}</h2>
                <p class="text-neon-blue font-semibold text-lg">{{ product.category }}</p>
                <p class="text-gray-300 mt-2">{{ product.description }}</p>
              </div>

              <!-- Specifications -->
              <div class="mb-6">
                <h3 class="text-xl font-semibold text-white mb-4">技术规格</h3>
                <div class="space-y-3">
                  <div v-for="spec in product.specifications" :key="spec.name" class="flex justify-between">
                    <span class="text-gray-400">{{ spec.name }}</span>
                    <span class="text-white font-medium">{{ spec.value }}</span>
                  </div>
                </div>
              </div>

              <!-- Service Info -->
              <div class="mb-6 p-4 bg-white/5 rounded-lg">
                <h3 class="text-lg font-semibold text-white mb-3">服务保障</h3>
                <div class="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div class="text-2xl font-bold text-neon-blue">{{ product.performance.speed }}</div>
                    <div class="text-xs text-gray-400 uppercase">质保期限</div>
                  </div>
                  <div>
                    <div class="text-2xl font-bold text-neon-purple">{{ product.performance.latency }}</div>
                    <div class="text-xs text-gray-400 uppercase">服务类型</div>
                  </div>
                </div>
              </div>

              <!-- Features -->
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-white mb-3">核心特性</h3>
                <ul class="space-y-2">
                  <li v-for="feature in product.features" :key="feature" class="flex items-center text-gray-300">
                    <CheckIcon class="w-4 h-4 text-neon-green mr-2 flex-shrink-0" />
                    {{ feature }}
                  </li>
                </ul>
              </div>

              <!-- Use Cases -->
              <div class="mb-6">
                <h3 class="text-lg font-semibold text-white mb-3">适用场景</h3>
                <div class="flex flex-wrap gap-2">
                  <span 
                    v-for="useCase in product.useCases" 
                    :key="useCase"
                    class="px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-full text-sm"
                  >
                    {{ useCase }}
                  </span>
                </div>
              </div>

              <!-- Pricing -->
              <div class="mb-6 p-4 bg-gradient-to-r from-neon-blue/10 to-neon-purple/10 rounded-lg border border-white/10">
                <div class="text-center">
                  <div class="text-3xl font-bold text-white mb-1">¥{{ product.price.toLocaleString() }}</div>
                  <div class="text-gray-400 text-sm">套餐价格</div>
                </div>
              </div>

              <!-- CTA -->
              <div class="mt-auto">
                <RouterLink to="/contact" class="btn-primary w-full text-center block">
                  联系客服
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Comparison Table -->
    <section class="section-padding bg-dark-300">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-4xl font-bold text-white text-center mb-12">
          套餐<span class="text-gradient">对比</span>
        </h2>
        
        <div class="overflow-x-auto">
          <table class="w-full card-glass">
            <thead>
              <tr class="border-b border-white/10">
                <th class="text-left p-4 text-white font-semibold">特性</th>
                <th v-for="product in products" :key="product.id" class="text-center p-4 text-white font-semibold">
                  {{ product.name }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="feature in comparisonFeatures" :key="feature.name" class="border-b border-white/5">
                <td class="p-4 text-gray-300 font-medium">{{ feature.name }}</td>
                <td v-for="value in feature.values" :key="value" class="p-4 text-center text-white">
                  {{ value }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { CheckIcon, CpuChipIcon, BoltIcon, RocketLaunchIcon } from '@heroicons/vue/24/outline'

const products = [
  {
    id: 'package-basic',
    name: '基础套餐',
    category: '标准版',
    description: '75T单板+Kmbox网络+6代融合器+单人定制冷门设备伪装固件，提供完整的DMA解决方案。',
    specifications: [
      { name: '75T单板', value: '1个' },
      { name: 'Kmbox网络', value: '1个' },
      { name: '6代融合器', value: '1个' },
      { name: '定制固件', value: '单人定制冷门设备伪装' }
    ],
    performance: {
      speed: '半年',
      latency: '质保期'
    },
    features: [
      '75T单板硬件',
      'Kmbox网络模块',
      '6代融合器',
      '单人定制冷门设备伪装固件',
      '半年质保服务'
    ],
    useCases: ['个人使用', '小型项目', '测试环境', '学习研究'],
    price: 3100,
    icon: CpuChipIcon
  },
  {
    id: 'package-premium',
    name: '高级套餐',
    category: '终身版',
    description: '75T单板+Kmbox网络+6代融合器+单人定制冷门设备伪装固件+永久免费刷固件，享受终身服务。',
    specifications: [
      { name: '75T单板', value: '1个' },
      { name: 'Kmbox网络', value: '1个' },
      { name: '6代融合器', value: '1个' },
      { name: '定制固件', value: '单人定制冷门设备伪装' }
    ],
    performance: {
      speed: '终身',
      latency: '质保期'
    },
    features: [
      '75T单板硬件',
      'Kmbox网络模块',
      '6代融合器',
      '单人定制冷门设备伪装固件',
      '永久免费刷固件服务',
      '终身质保服务'
    ],
    useCases: ['专业用户', '长期项目', '商业应用', '企业部署'],
    price: 7600,
    icon: BoltIcon
  }
]

const comparisonFeatures = [
  { name: '75T单板', values: ['✓', '✓'] },
  { name: 'Kmbox网络', values: ['✓', '✓'] },
  { name: '6代融合器', values: ['✓', '✓'] },
  { name: '定制固件', values: ['✓', '✓'] },
  { name: '免费刷固件', values: ['✗', '✓'] },
  { name: '质保期', values: ['半年', '终身'] },
  { name: '价格', values: ['¥3,100', '¥7,600'] }
]
</script>

<style scoped>
.product-detail-card {
  @apply opacity-0 transform translate-y-8;
  animation: slideInUp 0.8s ease-out forwards;
}

.product-detail-card:nth-child(1) { animation-delay: 0.1s; }
.product-detail-card:nth-child(2) { animation-delay: 0.3s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
