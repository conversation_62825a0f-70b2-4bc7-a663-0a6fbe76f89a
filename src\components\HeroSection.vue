<template>
  <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-gradient-to-br from-dark-300 via-dark-200 to-dark-400"></div>
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue/10 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl animate-float" style="animation-delay: -3s;"></div>
      <div class="absolute inset-0 bg-tech-pattern opacity-20"></div>
    </div>

    <!-- Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="space-y-8">
        <!-- Main Heading -->
        <div class="space-y-6">
          <!-- Brand Logo -->
          <div class="flex justify-center mb-8">
            <div class="relative">
              <div class="w-24 h-24 bg-gradient-to-br from-dark-400 to-dark-300 rounded-2xl flex items-center justify-center shadow-2xl shadow-neon-blue/30 animate-float border-2 border-white/10">
                <!-- Advanced Chip Design -->
                <div class="relative">
                  <div class="w-16 h-16 bg-gradient-to-br from-neon-blue via-neon-purple to-neon-pink rounded-lg"></div>
                  <div class="absolute inset-1 bg-white/10 rounded-md"></div>
                  <div class="absolute inset-2 grid grid-cols-4 gap-1">
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                    <div class="bg-white/70 rounded-[1px]"></div>
                  </div>
                  <!-- Connection traces -->
                  <div class="absolute -left-2 top-4 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -right-2 top-4 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -left-2 top-8 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -right-2 top-8 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -top-2 left-4 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -bottom-2 left-4 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -top-2 left-8 w-2 h-2 bg-white/90 rounded-full"></div>
                  <div class="absolute -bottom-2 left-8 w-2 h-2 bg-white/90 rounded-full"></div>
                </div>
              </div>
              <div class="absolute -top-2 -right-2 w-6 h-6 bg-neon-green rounded-full animate-pulse"></div>
              <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-neon-blue via-neon-purple to-neon-pink opacity-20 blur-xl"></div>
            </div>
          </div>

          <h1 class="text-5xl md:text-7xl font-bold leading-tight">
            <span class="block text-white mb-2">欢迎来到</span>
            <span class="block text-gradient text-6xl md:text-8xl">DMATop</span>
            <span class="block text-2xl md:text-3xl text-gray-300 font-normal mt-4">专业DMA硬件解决方案领导者</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            DMATop致力于提供业界顶级的DMA硬件套餐服务，包含75T单板、Kmbox网络、6代融合器和定制固件。
            <span class="block mt-2 text-neon-blue font-semibold">一年运营经验，专业服务，品质保障。</span>
          </p>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 py-12">
          <div class="text-center group">
            <div class="text-4xl md:text-5xl font-bold text-neon-blue mb-3 group-hover:scale-110 transition-transform duration-300" ref="stat1">0</div>
            <div class="text-gray-400 text-sm uppercase tracking-wide font-medium">年运营经验</div>
            <div class="text-xs text-gray-500 mt-1">自2024年成立</div>
          </div>
          <div class="text-center group">
            <div class="text-4xl md:text-5xl font-bold text-neon-purple mb-3 group-hover:scale-110 transition-transform duration-300" ref="stat2">0</div>
            <div class="text-gray-400 text-sm uppercase tracking-wide font-medium">套餐选择</div>
            <div class="text-xs text-gray-500 mt-1">专业定制方案</div>
          </div>
          <div class="text-center group">
            <div class="text-4xl md:text-5xl font-bold text-neon-green mb-3 group-hover:scale-110 transition-transform duration-300" ref="stat3">0</div>
            <div class="text-gray-400 text-sm uppercase tracking-wide font-medium">% 客户满意度</div>
            <div class="text-xs text-gray-500 mt-1">品质保障承诺</div>
          </div>
          <div class="text-center group">
            <div class="text-4xl md:text-5xl font-bold text-neon-pink mb-3 group-hover:scale-110 transition-transform duration-300" ref="stat4">0</div>
            <div class="text-gray-400 text-sm uppercase tracking-wide font-medium">+ 服务客户</div>
            <div class="text-xs text-gray-500 mt-1">持续增长中</div>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <RouterLink to="/products" class="btn-primary text-lg px-10 py-4">
            探索产品
          </RouterLink>
          <RouterLink to="/contact" class="btn-secondary text-lg px-10 py-4">
            联系客服
          </RouterLink>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-4 h-4 bg-neon-blue rounded-full animate-float opacity-60"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-neon-purple rounded-full animate-float opacity-40" style="animation-delay: -1s;"></div>
    <div class="absolute bottom-40 left-20 w-3 h-3 bg-neon-green rounded-full animate-float opacity-50" style="animation-delay: -2s;"></div>
    <div class="absolute bottom-20 right-10 w-5 h-5 bg-neon-pink rounded-full animate-float opacity-30" style="animation-delay: -4s;"></div>
  </section>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { RouterLink } from 'vue-router'
import { gsap } from 'gsap'

const stat1 = ref<HTMLElement>()
const stat2 = ref<HTMLElement>()
const stat3 = ref<HTMLElement>()
const stat4 = ref<HTMLElement>()



onMounted(() => {
  // Animate stats counter
  const animateCounter = (element: HTMLElement, target: number, suffix: string = '') => {
    gsap.to({ value: 0 }, {
      value: target,
      duration: 2,
      delay: 0.5,
      ease: "power2.out",
      onUpdate: function() {
        element.textContent = Math.round(this.targets()[0].value) + suffix
      }
    })
  }

  if (stat1.value) animateCounter(stat1.value, 1, '')
  if (stat2.value) animateCounter(stat2.value, 2, '')
  if (stat3.value) animateCounter(stat3.value, 99, '')
  if (stat4.value) animateCounter(stat4.value, 500, '')

  // Hero entrance animation
  gsap.timeline()
    .from('h1', { opacity: 0, y: 100, duration: 1, ease: "power3.out" })
    .from('p', { opacity: 0, y: 50, duration: 0.8, ease: "power2.out" }, "-=0.5")
    .from('.btn-primary, .btn-secondary', { opacity: 0, y: 30, duration: 0.6, stagger: 0.2, ease: "power2.out" }, "-=0.3")
})
</script>

<style scoped>
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}
</style>
