<template>
  <div class="pt-16">
    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-dark-300 via-dark-200 to-dark-400 relative overflow-hidden">
      <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue/10 rounded-full blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl"></div>
      </div>
      
      <div class="max-w-7xl mx-auto relative z-10 text-center">
        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
          <span class="text-gradient">DMATop</span> 技术支持中心
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
          DMATop致力于为客户提供全面的技术支持资源和专业协助，确保您的DMA硬件解决方案运行完美。
          <span class="block mt-2 text-neon-blue font-semibold">专业团队，7x24小时技术支持</span>
        </p>
      </div>
    </section>



    <!-- Knowledge Base -->
    <section class="section-padding bg-dark-200">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-white mb-6">
            <span class="text-gradient">参考说明</span>
          </h2>
          <p class="text-xl text-gray-300">
            查看我们的产品参考文档和使用说明。
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div v-for="guide in referenceGuides" :key="guide.id" class="knowledge-card">
            <div class="card-glass p-6 h-full">
              <div class="flex items-center mb-4">
                <component :is="guide.icon" class="w-8 h-8 text-neon-blue mr-3" />
                <h3 class="text-xl font-bold text-white">{{ guide.title }}</h3>
              </div>
              <p class="text-gray-300 mb-4">{{ guide.description }}</p>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-400">{{ guide.lastUpdated }}</span>
                <RouterLink :to="`/guide/${guide.id}`" class="text-neon-blue hover:text-white transition-colors duration-200">
                  查看详情 →
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Downloads Section -->
    <section class="section-padding bg-dark-300">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-white mb-6">
            <span class="text-gradient">下载中心</span>
          </h2>
          <p class="text-xl text-gray-300">
            获取DMAT5专用驱动程序、固件更新、配置工具和技术文档。
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div v-for="download in downloads" :key="download.name" class="download-item">
            <div class="card-glass p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="text-xl font-bold text-white mb-2">{{ download.name }}</h3>
                  <p class="text-gray-300 mb-3">{{ download.description }}</p>
                  <div class="flex items-center space-x-4 text-sm text-gray-400">
                    <span>版本 {{ download.version }}</span>
                    <span>{{ download.size }}</span>
                    <span>{{ download.date }}</span>
                  </div>
                </div>
                <button class="btn-secondary ml-4">
                  下载
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Support -->
    <section class="section-padding bg-gradient-to-r from-neon-blue/10 to-neon-purple/10">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-4xl font-bold text-white mb-6">
          仍需要帮助？
        </h2>
        <p class="text-xl text-gray-300 mb-8">
          我们的支持团队随时准备协助您解决任何问题或技术难题。
        </p>
        <div class="flex justify-center">
          <RouterLink to="/contact" class="btn-primary text-lg px-8 py-4">
            联系客服
          </RouterLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import {
  DocumentTextIcon,
  BookOpenIcon,
  WrenchScrewdriverIcon
} from '@heroicons/vue/24/outline'

// 直接定义参考指南数据
const referenceGuides = [
  {
    id: 'installation',
    title: 'DMAT5 安装指南',
    description: 'DMAT5硬件安装、75T板连接和Kmbox网络配置详细步骤。',
    lastUpdated: '2025年1月15日',
    icon: BookOpenIcon
  },
  {
    id: 'user-manual',
    title: 'DMAT5 用户手册',
    description: 'DMAT5完整操作指南，包含固件更新和性能优化设置。',
    lastUpdated: '2025年1月12日',
    icon: DocumentTextIcon
  },
  {
    id: 'troubleshooting',
    title: 'DMAT5 故障排除',
    description: 'DMAT5常见问题解决方案和专业技术支持指南。',
    lastUpdated: '2025年1月10日',
    icon: WrenchScrewdriverIcon
  }
]

const downloads = [
  {
    name: 'DMAT5 主控驱动程序',
    description: 'DMAT5主控板专用驱动程序，支持75T算力板和第六代集成器。',
    version: '5.2.1',
    size: '38.7 MB',
    date: '2025年1月15日'
  },
  {
    name: 'DMAT5 固件包',
    description: 'DMAT5定制固件，包含最新优化算法和性能提升。',
    version: '5.1.8',
    size: '124.3 MB',
    date: '2025年1月12日'
  },
  {
    name: 'Kmbox网络模块驱动',
    description: 'Kmbox网络模块专用驱动程序，确保稳定的网络连接。',
    version: '3.4.2',
    size: '15.6 MB',
    date: '2025年1月10日'
  }
]


</script>

<style scoped>
.support-card {
  @apply opacity-0 transform translate-y-8;
  animation: slideInUp 0.8s ease-out forwards;
}

.support-card:nth-child(1) { animation-delay: 0.1s; }
.support-card:nth-child(2) { animation-delay: 0.3s; }
.support-card:nth-child(3) { animation-delay: 0.5s; }

.faq-item {
  @apply opacity-0 transform translate-y-4;
  animation: fadeInUp 0.6s ease-out forwards;
}

.faq-item:nth-child(1) { animation-delay: 0.1s; }
.faq-item:nth-child(2) { animation-delay: 0.2s; }
.faq-item:nth-child(3) { animation-delay: 0.3s; }
.faq-item:nth-child(4) { animation-delay: 0.4s; }
.faq-item:nth-child(5) { animation-delay: 0.5s; }
.faq-item:nth-child(6) { animation-delay: 0.6s; }

.knowledge-card {
  @apply opacity-0 transform scale-95;
  animation: scaleIn 0.6s ease-out forwards;
}

.knowledge-card:nth-child(1) { animation-delay: 0.1s; }
.knowledge-card:nth-child(2) { animation-delay: 0.2s; }
.knowledge-card:nth-child(3) { animation-delay: 0.3s; }
.knowledge-card:nth-child(4) { animation-delay: 0.4s; }
.knowledge-card:nth-child(5) { animation-delay: 0.5s; }
.knowledge-card:nth-child(6) { animation-delay: 0.6s; }

.download-item {
  @apply opacity-0 transform translate-x-8;
  animation: slideInLeft 0.6s ease-out forwards;
}

.download-item:nth-child(odd) { animation-delay: 0.1s; }
.download-item:nth-child(even) { animation-delay: 0.3s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
