<template>
  <nav class="fixed top-0 left-0 right-0 z-50 bg-dark-300/80 backdrop-blur-md border-b border-white/10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <RouterLink to="/" class="flex items-center space-x-3 group">
            <div class="relative">
              <div class="w-10 h-10 bg-gradient-to-br from-dark-400 to-dark-300 rounded-lg flex items-center justify-center shadow-lg shadow-neon-blue/25 group-hover:shadow-neon-purple/40 transition-all duration-300 group-hover:scale-105 border border-white/10">
                <!-- Chip Icon -->
                <div class="relative">
                  <div class="w-6 h-6 bg-gradient-to-br from-neon-blue via-neon-purple to-neon-pink rounded-sm"></div>
                  <div class="absolute inset-0.5 bg-white/10 rounded-sm"></div>
                  <div class="absolute inset-1 grid grid-cols-2 gap-0.5">
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                  </div>
                </div>
              </div>
              <div class="absolute -top-1 -right-1 w-3 h-3 bg-neon-green rounded-full animate-pulse"></div>
            </div>
            <div class="flex flex-col">
              <span class="text-xl font-bold text-gradient leading-none">DMATop</span>
              <span class="text-xs text-gray-400 leading-none">专业DMA解决方案</span>
            </div>
          </RouterLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-8">
            <RouterLink
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              class="nav-link"
              :class="{ 'nav-link-active': $route.path === item.href }"
            >
              {{ item.name }}
            </RouterLink>
          </div>
        </div>

        <!-- CTA Button -->
        <div class="hidden md:block">
          <RouterLink to="/contact" class="btn-primary">
            立即开始
          </RouterLink>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="text-white hover:text-neon-blue transition-colors duration-200"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                v-if="!mobileMenuOpen"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                v-else
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div v-if="mobileMenuOpen" class="md:hidden bg-dark-200/95 backdrop-blur-md border-t border-white/10">
      <div class="px-2 pt-2 pb-3 space-y-1">
        <RouterLink
          v-for="item in navigation"
          :key="item.name"
          :to="item.href"
          class="mobile-nav-link"
          :class="{ 'mobile-nav-link-active': $route.path === item.href }"
          @click="mobileMenuOpen = false"
        >
          {{ item.name }}
        </RouterLink>
        <RouterLink
          to="/contact"
          class="block w-full text-center btn-primary mt-4"
          @click="mobileMenuOpen = false"
        >
          立即开始
        </RouterLink>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink } from 'vue-router'

const mobileMenuOpen = ref(false)

const navigation = [
  { name: '首页', href: '/' },
  { name: '产品', href: '/products' },
  { name: '技术支持', href: '/support' },
  { name: '联系我们', href: '/contact' },
]
</script>

<style scoped>
.nav-link {
  @apply text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 50%;
  background: linear-gradient(90deg, #00d4ff, #8b5cf6);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link-active::after {
  width: 100%;
}

.nav-link-active {
  @apply text-white;
}

.mobile-nav-link {
  @apply text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200;
}

.mobile-nav-link-active {
  @apply text-white bg-white/10;
}
</style>
