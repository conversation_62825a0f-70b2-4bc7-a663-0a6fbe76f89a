<template>
  <section id="product-overview" class="section-padding bg-dark-200">
    <div class="max-w-7xl mx-auto">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
          我们的<span class="text-gradient">服务套餐</span>
        </h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">
          提供完整的DMA硬件解决方案套餐，包含75T单板、Kmbox网络、6代融合器和定制固件服务。
        </p>
      </div>

      <!-- Product Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <div 
          v-for="(product, index) in products" 
          :key="product.id"
          class="product-card"
          :style="{ animationDelay: `${index * 0.2}s` }"
        >
          <div class="card-glass h-full">
            <!-- Product Image/Icon -->
            <div class="mb-6">
              <div class="w-16 h-16 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl flex items-center justify-center mb-4">
                <component :is="product.icon" class="w-8 h-8 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">{{ product.name }}</h3>
              <p class="text-neon-blue font-semibold">{{ product.category }}</p>
            </div>

            <!-- Features -->
            <div class="mb-6">
              <ul class="space-y-2">
                <li v-for="feature in product.features" :key="feature" class="flex items-center text-gray-300">
                  <CheckIcon class="w-4 h-4 text-neon-green mr-2 flex-shrink-0" />
                  {{ feature }}
                </li>
              </ul>
            </div>

            <!-- Service Info -->
            <div class="mb-6 p-4 bg-white/5 rounded-lg">
              <div class="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div class="text-2xl font-bold text-neon-blue">{{ product.performance.speed }}</div>
                  <div class="text-xs text-gray-400 uppercase">质保期限</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-neon-purple">¥{{ product.price.toLocaleString() }}</div>
                  <div class="text-xs text-gray-400 uppercase">套餐价格</div>
                </div>
              </div>
            </div>

            <!-- CTA -->
            <div class="mt-auto">
              <RouterLink :to="`/products#${product.id}`" class="btn-secondary w-full text-center block">
                了解更多
              </RouterLink>
            </div>
          </div>
        </div>
      </div>

      <!-- Technology Showcase -->
      <div class="mt-20 text-center">
        <h3 class="text-3xl font-bold text-white mb-8">核心组件优势</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div v-for="tech in technologies" :key="tech.name" class="tech-item">
            <div class="w-12 h-12 bg-gradient-to-r from-neon-blue to-neon-purple rounded-lg flex items-center justify-center mx-auto mb-3">
              <component :is="tech.icon" class="w-6 h-6 text-white" />
            </div>
            <h4 class="text-white font-semibold mb-1">{{ tech.name }}</h4>
            <p class="text-gray-400 text-sm">{{ tech.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import { CheckIcon, CpuChipIcon, BoltIcon, ShieldCheckIcon, RocketLaunchIcon } from '@heroicons/vue/24/outline'

const products = [
  {
    id: 'package-basic',
    name: '基础套餐',
    category: '标准版',
    features: [
      '75T单板硬件',
      'Kmbox网络模块',
      '6代融合器',
      '单人定制冷门设备伪装固件',
      '半年质保服务'
    ],
    performance: {
      speed: '半年'
    },
    price: 3100,
    icon: CpuChipIcon
  },
  {
    id: 'package-premium',
    name: '高级套餐',
    category: '终身版',
    features: [
      '75T单板硬件',
      'Kmbox网络模块',
      '6代融合器',
      '单人定制冷门设备伪装固件',
      '永久免费刷固件服务',
      '终身质保服务'
    ],
    performance: {
      speed: '终身'
    },
    price: 7600,
    icon: BoltIcon
  }
]

const technologies = [
  {
    name: '75T单板',
    description: '高性能硬件',
    icon: CpuChipIcon
  },
  {
    name: 'Kmbox网络',
    description: '网络模块',
    icon: BoltIcon
  },
  {
    name: '6代融合器',
    description: '先进技术',
    icon: ShieldCheckIcon
  },
  {
    name: '定制固件',
    description: '专属服务',
    icon: RocketLaunchIcon
  }
]
</script>

<style scoped>
.product-card {
  @apply opacity-0 transform translate-y-8;
  animation: slideInUp 0.8s ease-out forwards;
}

.tech-item {
  @apply opacity-0 transform translate-y-4;
  animation: fadeInUp 0.6s ease-out forwards;
  animation-delay: 1s;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card:hover .card-glass {
  @apply transform scale-105;
}
</style>
