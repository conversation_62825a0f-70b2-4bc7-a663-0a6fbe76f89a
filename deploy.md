# 部署指南

## GitHub 上传步骤

### 1. 初始化Git仓库（如果还没有）
```bash
git init
```

### 2. 添加所有文件
```bash
git add .
```

### 3. 提交代码
```bash
git commit -m "Initial commit: DMATop website"
```

### 4. 添加远程仓库
```bash
git remote add origin https://github.com/你的用户名/dmatop-website.git
```

### 5. 推送到GitHub
```bash
git push -u origin main
```

## Vercel 部署步骤

### 方法一：通过Vercel网站部署

1. 访问 [vercel.com](https://vercel.com)
2. 使用GitHub账号登录
3. 点击 "New Project"
4. 选择您的GitHub仓库 `dmatop-website`
5. Vercel会自动检测到这是一个Vite项目
6. 确认配置：
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`
7. 点击 "Deploy"

### 方法二：通过Vercel CLI部署

1. 安装Vercel CLI
```bash
npm i -g vercel
```

2. 登录Vercel
```bash
vercel login
```

3. 在项目根目录运行
```bash
vercel
```

4. 按照提示完成配置

## 环境变量配置（如需要）

如果您的项目需要环境变量，在Vercel项目设置中添加：

- `NODE_ENV=production`
- 其他自定义环境变量

## 自动部署

一旦设置完成，每次推送到GitHub主分支时，Vercel会自动重新部署网站。

## 自定义域名

在Vercel项目设置中的"Domains"部分可以添加自定义域名。

## 注意事项

- 确保所有依赖都在 `package.json` 中正确声明
- 检查构建命令 `npm run build` 能正常运行
- 确保 `dist` 目录被正确生成
- 项目已配置好 `vercel.json` 文件，支持Vue Router的SPA路由
