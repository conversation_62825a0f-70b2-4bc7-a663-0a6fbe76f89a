// 参考说明内容数据
interface Guide {
  id: string
  title: string
  description: string
  lastUpdated: string
  content: string
}

export const guidesData: Record<string, Guide> = {
  installation: {
    id: 'installation',
    title: '安装指南',
    description: '详细的产品安装步骤和配置说明。',
    lastUpdated: '2025年1月15日',
    content: `# DMA硬件安装指南

## 系统要求

在开始安装之前，请确保您的系统满足以下要求：

### 硬件要求
- PCIe 4.0 或更高版本插槽
- 最少 16GB 系统内存
- 500W 或更高功率电源
- 充足的机箱空间和散热

### 软件要求
- Windows 10/11 (64位)
- Linux Ubuntu 20.04+ 或 CentOS 8+
- macOS 12+ (部分型号支持)

## 安装步骤

### 第一步：准备工作
1. 关闭计算机电源并断开所有电缆
2. 打开机箱侧板
3. 找到合适的PCIe插槽（建议使用最靠近CPU的x16插槽）

### 第二步：硬件安装
1. 小心取出DMA硬件，避免触摸金手指部分
2. 将DMA硬件插入PCIe插槽，确保完全插入
3. 使用螺丝固定挡板
4. 连接必要的电源线（如果需要）

### 第三步：驱动安装
1. 启动计算机
2. 从官网下载最新驱动程序
3. 以管理员身份运行安装程序
4. 按照安装向导完成安装
5. 重启计算机

### 第四步：验证安装
1. 打开设备管理器
2. 确认DMA设备正确识别
3. 运行性能测试工具
4. 检查温度和功耗是否正常

## 故障排除

如果遇到安装问题，请检查：
- PCIe插槽是否完全插入
- 电源是否充足
- 驱动程序是否正确安装
- BIOS设置是否正确

如需更多帮助，请联系我们的技术支持团队。`
  },

  'user-manual': {
    id: 'user-manual',
    title: '用户手册',
    description: '完整的产品使用说明和操作指南。',
    lastUpdated: '2025年1月12日',
    content: `# DMA硬件用户手册

## 产品概述

DMA（Direct Memory Access）硬件是一款高性能的数据传输加速器，专为需要大量数据处理的应用而设计。

## 主要功能

### 高速数据传输
- 支持PCIe 4.0接口
- 最高传输速度可达64GB/s
- 低延迟数据处理

### 智能缓存管理
- 自适应缓存算法
- 多级缓存架构
- 实时性能优化

### 兼容性支持
- 支持多种操作系统
- 兼容主流开发框架
- 提供完整的API接口

## 基本操作

### 启动和初始化
1. 确保驱动程序已正确安装
2. 打开DMA控制面板
3. 检查设备状态
4. 进行初始化配置

### 性能配置
1. 根据应用需求调整缓存大小
2. 设置数据传输优先级
3. 配置温度和功耗限制
4. 启用性能监控

### 日常使用
1. 监控设备温度和性能
2. 定期更新驱动程序
3. 清理临时文件和缓存
4. 备份重要配置

## 性能优化建议

### 系统配置
- 确保充足的系统内存
- 使用高速SSD存储
- 保持良好的散热环境
- 定期清理系统垃圾

### 应用优化
- 合理分配内存资源
- 优化数据传输模式
- 使用批处理提高效率
- 避免频繁的小数据传输

## 维护保养

### 定期维护
- 每月清理设备表面灰尘
- 检查散热器和风扇
- 更新驱动程序和固件
- 备份配置文件

### 注意事项
- 避免在高温环境下长时间使用
- 不要随意修改硬件设置
- 定期检查电源连接
- 保持良好的通风环境

如有任何使用问题，请参考故障排除指南或联系技术支持。`
  },

  troubleshooting: {
    id: 'troubleshooting',
    title: '故障排除',
    description: '常见问题解决方案和技术支持指南。',
    lastUpdated: '2025年1月10日',
    content: `# 故障排除指南

## 常见问题

### 设备无法识别

**症状：** 设备管理器中看不到DMA设备

**可能原因：**
- 硬件未正确安装
- 驱动程序未安装或损坏
- PCIe插槽故障
- 电源供应不足

**解决方案：**
1. 重新安装硬件，确保完全插入
2. 重新安装最新驱动程序
3. 尝试其他PCIe插槽
4. 检查电源功率是否充足

### 性能下降

**症状：** 数据传输速度明显降低

**可能原因：**
- 温度过高导致降频
- 缓存配置不当
- 系统资源不足
- 驱动程序版本过旧

**解决方案：**
1. 检查设备温度，改善散热
2. 重新配置缓存设置
3. 关闭不必要的后台程序
4. 更新到最新驱动程序

### 系统不稳定

**症状：** 系统频繁死机或蓝屏

**可能原因：**
- 驱动程序冲突
- 硬件兼容性问题
- 电源不稳定
- 内存不足

**解决方案：**
1. 卸载并重新安装驱动程序
2. 检查硬件兼容性列表
3. 更换更高功率的电源
4. 增加系统内存

### 温度过高

**症状：** 设备温度超过安全范围

**可能原因：**
- 散热器安装不当
- 机箱通风不良
- 环境温度过高
- 风扇故障

**解决方案：**
1. 重新安装散热器
2. 改善机箱通风
3. 降低环境温度
4. 更换故障风扇

## 错误代码

### 错误代码 0x001
**描述：** 初始化失败
**解决方案：** 重启系统并重新安装驱动程序

### 错误代码 0x002
**描述：** 内存分配失败
**解决方案：** 增加系统内存或关闭其他程序

### 错误代码 0x003
**描述：** 硬件通信错误
**解决方案：** 检查硬件连接，重新插拔设备

## 诊断工具

### 系统诊断
使用我们提供的诊断工具可以快速定位问题：
1. 下载DMA诊断工具
2. 以管理员身份运行
3. 选择全面检测
4. 查看检测报告

### 性能测试
定期进行性能测试可以及早发现问题：
1. 运行基准测试
2. 记录性能数据
3. 与标准值对比
4. 分析性能趋势

## 联系支持

如果以上方法无法解决问题，请联系我们的技术支持团队：

- 准备设备型号和序列号
- 描述具体问题和错误信息
- 提供系统配置信息
- 说明已尝试的解决方案

我们的技术支持团队将为您提供专业的技术支持。`
  }
}

// 获取指南数据的辅助函数
export const getGuideById = (id: string): Guide | null => {
  return guidesData[id] || null
}

// 获取所有指南列表
export const getAllGuides = (): Guide[] => {
  return Object.values(guidesData)
}
