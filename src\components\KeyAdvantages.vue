<template>
  <section class="section-padding bg-dark-300 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
      <div class="absolute top-0 left-1/3 w-96 h-96 bg-neon-purple/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-0 right-1/3 w-96 h-96 bg-neon-blue/5 rounded-full blur-3xl"></div>
    </div>

    <div class="max-w-7xl mx-auto relative z-10">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
          为什么选择<span class="text-gradient">DMA</span>
        </h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">
          选择DMAT5，享受稳定可靠、高效运行的专业DMA解决方案。
        </p>
      </div>

      <!-- Advantages Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
        <!-- Left Side - Advantages List -->
        <div class="space-y-8">
          <div 
            v-for="(advantage, index) in advantages" 
            :key="advantage.title"
            class="advantage-item flex items-start space-x-4"
            :style="{ animationDelay: `${index * 0.2}s` }"
          >
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-r from-neon-blue to-neon-purple rounded-xl flex items-center justify-center">
                <component :is="advantage.icon" class="w-6 h-6 text-white" />
              </div>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white mb-2">{{ advantage.title }}</h3>
              <p class="text-gray-300 leading-relaxed">{{ advantage.description }}</p>
              <div class="mt-3 flex items-center text-neon-blue">
                <span class="text-2xl font-bold mr-2">{{ advantage.metric }}</span>
                <span class="text-sm">{{ advantage.unit }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Performance Chart -->
        <div class="card-glass p-8">
          <h3 class="text-2xl font-bold text-white mb-6 text-center">性能对比</h3>
          <div class="space-y-6">
            <div v-for="metric in performanceMetrics" :key="metric.name" class="performance-bar">
              <div class="flex justify-between items-center mb-2">
                <span class="text-white font-medium">{{ metric.name }}</span>
                <span class="text-neon-blue font-bold">{{ metric.value }}%</span>
              </div>
              <div class="w-full bg-gray-700 rounded-full h-3">
                <div 
                  class="bg-gradient-to-r from-neon-blue to-neon-purple h-3 rounded-full transition-all duration-1000 ease-out"
                  :style="{ width: `${metric.value}%` }"
                ></div>
              </div>
            </div>
          </div>
          <p class="text-gray-400 text-sm mt-6 text-center">
            * 与行业标准解决方案相比
          </p>
        </div>
      </div>

      <!-- Bottom CTA Section -->
      <div class="text-center bg-gradient-to-r from-neon-blue/10 to-neon-purple/10 rounded-2xl p-8 border border-white/10">
        <h3 class="text-3xl font-bold text-white mb-4">选择稳定高效的DMAT5</h3>
        <p class="text-gray-300 mb-6 max-w-2xl mx-auto">
          一年来，我们专注于为客户提供稳定可靠、高效运行的专业DMA解决方案。
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <RouterLink to="/products" class="btn-primary">
            查看所有产品
          </RouterLink>
          <RouterLink to="/contact" class="btn-secondary">
            联系客服
          </RouterLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterLink } from 'vue-router'
import { 
  BoltIcon, 
  ShieldCheckIcon, 
  CpuChipIcon, 
  RocketLaunchIcon,
  ClockIcon,
  CurrencyDollarIcon
} from '@heroicons/vue/24/outline'

const advantages = [
  {
    title: '稳定可靠',
    description: 'DMAT5采用工业级设计标准，7x24小时稳定运行，故障率低于0.1%，确保业务连续性。',
    metric: '99.9%',
    unit: '稳定性',
    icon: ShieldCheckIcon
  },
  {
    title: '高效处理',
    description: '75T算力板配合第六代集成器，处理效率提升40%，大幅降低运行成本和能耗。',
    metric: '40%',
    unit: '效率提升',
    icon: BoltIcon
  },
  {
    title: '快速响应',
    description: '优化的硬件架构和定制固件，实现毫秒级响应速度，满足实时处理需求。',
    metric: '<5',
    unit: '毫秒',
    icon: ClockIcon
  },
  {
    title: '专业服务',
    description: '提供专业技术支持和终身固件更新服务，确保设备始终保持最佳性能状态。',
    metric: '7x24',
    unit: '小时支持',
    icon: CpuChipIcon
  }
]

const performanceMetrics = [
  { name: '系统稳定性', value: 99 },
  { name: '处理效率', value: 95 },
  { name: '响应速度', value: 92 },
  { name: '能耗优化', value: 88 },
  { name: '运行可靠性', value: 97 }
]

onMounted(() => {
  // Animate performance bars
  setTimeout(() => {
    const bars = document.querySelectorAll('.performance-bar .bg-gradient-to-r')
    bars.forEach((bar, index) => {
      setTimeout(() => {
        (bar as HTMLElement).style.width = `${performanceMetrics[index].value}%`
      }, index * 200)
    })
  }, 500)
})
</script>

<style scoped>
.advantage-item {
  @apply opacity-0 transform translate-x-8;
  animation: slideInLeft 0.8s ease-out forwards;
}

.performance-bar .bg-gradient-to-r {
  width: 0%;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
