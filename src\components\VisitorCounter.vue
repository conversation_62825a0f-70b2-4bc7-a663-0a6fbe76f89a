<template>
  <div class="fixed bottom-6 right-6 z-50">
    <!-- 悬浮访问统计卡片 -->
    <div
      class="visitor-counter bg-dark-200/90 backdrop-blur-md border border-white/10 rounded-2xl p-4 shadow-2xl transition-all duration-300 hover:scale-105 hover:shadow-neon-blue/20 cursor-pointer"
      :class="{ 'animate-pulse': isUpdating }"
      @click="updateVisitorData"
      title="点击刷新数据"
    >
      <!-- 标题 -->
      <div class="flex items-center mb-3">
        <div class="w-2 h-2 bg-neon-green rounded-full animate-pulse mr-2"></div>
        <span class="text-xs text-gray-300 font-medium">实时访问</span>
      </div>
      
      <!-- 访问数据 -->
      <div class="space-y-2">
        <!-- 总访问量 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-400">总访问量</span>
          <div class="flex items-center">
            <svg class="w-3 h-3 text-neon-blue mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
              <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-bold text-white">{{ formatNumber(totalVisitors) }}</span>
          </div>
        </div>
        
        <!-- 今日访问 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-400">今日访问</span>
          <div class="flex items-center">
            <svg class="w-3 h-3 text-neon-purple mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
            <span class="text-sm font-bold text-white">{{ formatNumber(todayVisitors) }}</span>
          </div>
        </div>
        
        <!-- 在线人数 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-400">在线人数</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-neon-green rounded-full animate-pulse mr-1"></div>
            <span class="text-sm font-bold text-neon-green">{{ onlineUsers }}</span>
          </div>
        </div>
      </div>
      
      <!-- 底部装饰线 -->
      <div class="mt-3 h-px bg-gradient-to-r from-transparent via-neon-blue/30 to-transparent"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const totalVisitors = ref(12847)
const todayVisitors = ref(156)
const onlineUsers = ref(8)
const isUpdating = ref(false)

// 更新间隔
let updateInterval: number

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toLocaleString()
}

// 模拟访问数据更新
const updateVisitorData = () => {
  isUpdating.value = true

  // 模拟数据变化
  setTimeout(() => {
    // 总访问量缓慢增长（更真实的增长模式）
    if (Math.random() > 0.8) {
      totalVisitors.value += Math.floor(Math.random() * 2) + 1

      // 同步更新localStorage
      const dataToStore = {
        total: totalVisitors.value,
        today: todayVisitors.value,
        lastDate: new Date().toDateString()
      }
      localStorage.setItem('dmatop-visitor-data', JSON.stringify(dataToStore))
    }

    // 今日访问量增长
    if (Math.random() > 0.7) {
      todayVisitors.value += Math.floor(Math.random() * 2) + 1

      // 同步更新localStorage
      const dataToStore = {
        total: totalVisitors.value,
        today: todayVisitors.value,
        lastDate: new Date().toDateString()
      }
      localStorage.setItem('dmatop-visitor-data', JSON.stringify(dataToStore))
    }

    // 在线人数波动（更自然的波动）
    const change = Math.floor(Math.random() * 3) - 1 // -1 到 +1 的变化
    onlineUsers.value = Math.max(3, Math.min(25, onlineUsers.value + change))

    isUpdating.value = false
  }, 500)
}

// 初始化访问统计
const initVisitorCounter = () => {
  // 从localStorage获取或初始化访问数据
  const stored = localStorage.getItem('dmatop-visitor-data')
  if (stored) {
    try {
      const data = JSON.parse(stored)
      totalVisitors.value = data.total || 12847
      
      // 检查是否是新的一天
      const today = new Date().toDateString()
      if (data.lastDate === today) {
        todayVisitors.value = data.today || 1
      } else {
        todayVisitors.value = 1
      }
    } catch (e) {
      console.log('访问数据解析失败，使用默认值')
    }
  }
  
  // 增加访问计数
  totalVisitors.value += 1
  todayVisitors.value += 1
  
  // 保存到localStorage
  const dataToStore = {
    total: totalVisitors.value,
    today: todayVisitors.value,
    lastDate: new Date().toDateString()
  }
  localStorage.setItem('dmatop-visitor-data', JSON.stringify(dataToStore))
}

onMounted(() => {
  // 初始化访问统计
  initVisitorCounter()
  
  // 设置定期更新（每30秒）
  updateInterval = setInterval(updateVisitorData, 30000)
  
  // 随机初始在线人数
  onlineUsers.value = Math.floor(Math.random() * 15) + 5
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.visitor-counter {
  min-width: 140px;
  backdrop-filter: blur(12px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.visitor-counter:hover {
  box-shadow: 
    0 12px 40px rgba(0, 212, 255, 0.15),
    0 0 0 1px rgba(0, 212, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .visitor-counter {
    bottom: 1rem;
    right: 1rem;
    min-width: 120px;
    padding: 0.75rem;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.visitor-counter {
  animation: slideInUp 0.6s ease-out;
}
</style>
