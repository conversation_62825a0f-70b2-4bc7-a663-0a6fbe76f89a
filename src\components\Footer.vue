<template>
  <footer class="bg-dark-400 border-t border-white/10">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-3 mb-6">
            <div class="relative">
              <div class="w-12 h-12 bg-gradient-to-br from-dark-400 to-dark-300 rounded-xl flex items-center justify-center shadow-lg shadow-neon-blue/25 border border-white/10">
                <!-- Chip Icon -->
                <div class="relative">
                  <div class="w-8 h-8 bg-gradient-to-br from-neon-blue via-neon-purple to-neon-pink rounded"></div>
                  <div class="absolute inset-0.5 bg-white/10 rounded"></div>
                  <div class="absolute inset-1 grid grid-cols-3 gap-0.5">
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                    <div class="bg-white/60 rounded-[1px]"></div>
                  </div>
                  <!-- Connection traces -->
                  <div class="absolute -left-1 top-2 w-1 h-1 bg-white/80 rounded-full"></div>
                  <div class="absolute -right-1 top-2 w-1 h-1 bg-white/80 rounded-full"></div>
                  <div class="absolute -top-1 left-2 w-1 h-1 bg-white/80 rounded-full"></div>
                  <div class="absolute -bottom-1 left-2 w-1 h-1 bg-white/80 rounded-full"></div>
                </div>
              </div>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-neon-green rounded-full animate-pulse"></div>
            </div>
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-gradient leading-none">DMATop</span>
              <span class="text-sm text-gray-400 leading-none mt-1">专业DMA解决方案领导者</span>
            </div>
          </div>
          <p class="text-gray-400 mb-4 max-w-md leading-relaxed">
            DMATop致力于提供业界领先的高性能DMA硬件解决方案，
            包含完整的75T单板、Kmbox网络、6代融合器和定制固件服务。
            以创新技术驱动，为客户提供卓越的计算性能体验。
          </p>
          <div class="flex space-x-4">
            <!-- X (Twitter) -->
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors duration-200">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </a>
            <!-- LinkedIn -->
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors duration-200">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            <!-- Telegram -->
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors duration-200">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-white font-semibold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><RouterLink to="/" class="footer-link">首页</RouterLink></li>
            <li><RouterLink to="/products" class="footer-link">产品</RouterLink></li>
            <li><RouterLink to="/support" class="footer-link">技术支持</RouterLink></li>
          </ul>
        </div>

        <!-- Support -->
        <div>
          <h3 class="text-white font-semibold mb-4">技术支持</h3>
          <ul class="space-y-2">
            <li><a href="#" class="footer-link">技术文档</a></li>
            <li><a href="#" class="footer-link">软件下载</a></li>
            <li><a href="#" class="footer-link">技术支持</a></li>
            <li><RouterLink to="/contact" class="footer-link">联系我们</RouterLink></li>
          </ul>
        </div>
      </div>

      <div class="mt-8 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">
          © 2025 DMATop科技有限公司. 保留所有权利.
        </p>
        <div class="flex space-x-6 mt-4 md:mt-0">
          <a href="#" class="footer-link text-sm">隐私政策</a>
          <a href="#" class="footer-link text-sm">服务条款</a>
          <a href="#" class="footer-link text-sm">Cookie政策</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>

<style scoped>
.footer-link {
  @apply text-gray-400 hover:text-neon-blue transition-colors duration-200;
}
</style>
