<template>
  <!-- 这个组件不需要模板，因为使用SweetAlert2 -->
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// 声明全局SweetAlert2类型
declare global {
  interface Window {
    Swal: any
  }
}

// 显示欢迎提醒
const showWelcomeAlert = () => {
  // 检查是否已经显示过提醒（24小时内不重复显示）
  const lastShown = localStorage.getItem('dmatop-welcome-shown')
  const now = new Date().getTime()
  const oneDay = 24 * 60 * 60 * 1000 // 24小时

  if (lastShown && (now - parseInt(lastShown)) < oneDay) {
    return // 24小时内已显示过，不再显示
  }

  // 延迟1秒显示，让页面先加载完成
  setTimeout(() => {
    if (window.Swal) {
      window.Swal.fire({
        title: '欢迎来到 DMATop',
        html: `
          <div style="text-align: left; line-height: 1.6; color: #374151;">
            <div style="margin-bottom: 16px;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <svg style="width: 20px; height: 20px; color: #3B82F6; margin-right: 8px;" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
                <strong style="color: #1F2937;">服务咨询</strong>
              </div>
              <p style="margin: 0; padding-left: 28px; color: #6B7280;">
                如需了解详细服务信息，请添加客服QQ获取专业咨询
              </p>
            </div>
            
            <div style="margin-bottom: 16px;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <svg style="width: 20px; height: 20px; color: #10B981; margin-right: 8px;" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                </svg>
                <strong style="color: #1F2937;">安全交易</strong>
              </div>
              <p style="margin: 0; padding-left: 28px; color: #6B7280;">
                为保障您的权益，建议通过网购大平台进行交易
              </p>
            </div>

            <div style="background: linear-gradient(135deg, #EBF8FF 0%, #F0FDF4 100%); padding: 12px; border-radius: 8px; border-left: 4px solid #3B82F6;">
              <p style="margin: 0; color: #1E40AF; font-size: 14px;">
                <strong>💡 温馨提示：</strong>我们致力于为您提供专业、安全的DMA硬件解决方案服务
              </p>
            </div>
          </div>
        `,
        icon: 'info',
        iconColor: '#3B82F6',
        confirmButtonText: '我知道了',
        confirmButtonColor: '#3B82F6',
        showCancelButton: true,
        cancelButtonText: '联系客服',
        cancelButtonColor: '#8B5CF6',
        width: '480px',
        padding: '2rem',
        background: '#ffffff',
        backdrop: 'rgba(0, 0, 0, 0.4)',
        allowOutsideClick: true,
        allowEscapeKey: true,
        showClass: {
          popup: 'animate__animated animate__fadeInDown animate__faster'
        },
        hideClass: {
          popup: 'animate__animated animate__fadeOutUp animate__faster'
        },
        customClass: {
          popup: 'dmatop-welcome-popup',
          title: 'dmatop-welcome-title',
          htmlContainer: 'dmatop-welcome-content',
          confirmButton: 'dmatop-welcome-btn-confirm',
          cancelButton: 'dmatop-welcome-btn-cancel'
        }
      }).then((result: any) => {
        if (result.isDismissed && result.dismiss === window.Swal.DismissReason.cancel) {
          // 用户点击了"联系客服"按钮
          // 跳转到联系页面
          window.location.href = '/contact'
        }
        
        // 记录已显示时间
        localStorage.setItem('dmatop-welcome-shown', now.toString())
      })

      // 添加自定义样式
      const style = document.createElement('style')
      style.textContent = `
        .dmatop-welcome-popup {
          border-radius: 16px !important;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        }
        
        .dmatop-welcome-title {
          font-size: 1.5rem !important;
          font-weight: 700 !important;
          color: #1F2937 !important;
          margin-bottom: 1rem !important;
        }
        
        .dmatop-welcome-content {
          font-size: 15px !important;
        }
        
        .dmatop-welcome-btn-confirm {
          border-radius: 8px !important;
          padding: 10px 24px !important;
          font-weight: 600 !important;
          transition: all 0.2s ease !important;
          background-color: #3B82F6 !important;
          border: none !important;
          color: white !important;
        }

        .dmatop-welcome-btn-confirm:hover {
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
          background-color: #2563EB !important;
        }

        .dmatop-welcome-btn-cancel {
          border-radius: 8px !important;
          padding: 10px 24px !important;
          font-weight: 600 !important;
          transition: all 0.2s ease !important;
          background-color: #8B5CF6 !important;
          border: none !important;
          color: white !important;
        }

        .dmatop-welcome-btn-cancel:hover {
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4) !important;
          background-color: #7C3AED !important;
        }
        
        @media (max-width: 640px) {
          .dmatop-welcome-popup {
            width: 90% !important;
            margin: 0 auto !important;
          }
        }
      `
      document.head.appendChild(style)
    }
  }, 1000)
}

onMounted(() => {
  // 等待SweetAlert2加载完成
  const checkSwal = () => {
    if (window.Swal) {
      showWelcomeAlert()
    } else {
      setTimeout(checkSwal, 100)
    }
  }
  checkSwal()
})
</script>

<style scoped>
/* 这个组件主要使用SweetAlert2，不需要额外样式 */
</style>
