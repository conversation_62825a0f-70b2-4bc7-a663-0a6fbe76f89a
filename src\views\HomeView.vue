<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { gsap } from 'gsap'
import HeroSection from '../components/HeroSection.vue'
import ProductOverview from '../components/ProductOverview.vue'
import KeyAdvantages from '../components/KeyAdvantages.vue'
import Testimonials from '../components/Testimonials.vue'
import CallToAction from '../components/CallToAction.vue'

onMounted(() => {
  // Initialize scroll animations
  gsap.fromTo('.animate-on-scroll',
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      duration: 1,
      stagger: 0.2
    }
  )
})
</script>

<template>
  <div class="pt-16">
    <HeroSection />
    <ProductOverview class="animate-on-scroll" />
    <KeyAdvantages class="animate-on-scroll" />
    <Testimonials class="animate-on-scroll" />
    <CallToAction class="animate-on-scroll" />
  </div>
</template>
