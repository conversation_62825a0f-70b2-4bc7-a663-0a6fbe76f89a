# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Build outputs
build/
out/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Vue.js specific
.vue-tsc-cache/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.bak
*.backup
